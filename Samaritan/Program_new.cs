namespace Samaritan;

using System.Runtime.CompilerServices;
using Spectre.Console;

internal static class Program
{
    [ModuleInitializer]
    public static void Initialize()
    {
        Console.Title = nameof(Samaritan);
    }
    
    public static async Task Main(string[] args)
    {
        AnsiConsole.Write(
            new FigletText("Samaritan")
                .LeftJustified()
                .Color(Color.Blue));

        await ShowMainMenuAsync();
    }

    private static async Task ShowMainMenuAsync()
    {
        while (true)
        {
            AnsiConsole.Clear();
            
            AnsiConsole.Write(
                new Rule("[blue]Main Menu[/]")
                    .RuleStyle("grey")
                    .LeftJustified());

            var choice = AnsiConsole.Prompt(
                new SelectionPrompt<string>()
                    .Title("What would you like to do?")
                    .PageSize(10)
                    .AddChoices(new[]
                    {
                        "Simulate File Operation",
                        "Simulate Network Request", 
                        "Show Progress Demo",
                        "Display System Info",
                        "Exit"
                    }));

            switch (choice)
            {
                case "Simulate File Operation":
                    await SimulateFileOperationAsync();
                    break;
                case "Simulate Network Request":
                    await SimulateNetworkRequestAsync();
                    break;
                case "Show Progress Demo":
                    await ShowProgressDemoAsync();
                    break;
                case "Display System Info":
                    await DisplaySystemInfoAsync();
                    break;
                case "Exit":
                    AnsiConsole.MarkupLine("[green]Goodbye![/]");
                    return;
            }

            AnsiConsole.WriteLine();
            AnsiConsole.MarkupLine("[grey]Press any key to continue...[/]");
            Console.ReadKey(true);
        }
    }

    private static async Task SimulateFileOperationAsync()
    {
        AnsiConsole.MarkupLine("[yellow]Simulating file operation...[/]");
        
        await AnsiConsole.Status()
            .StartAsync("Reading files...", async ctx =>
            {
                ctx.Status("Scanning directory...");
                await Task.Delay(1000);
                
                ctx.Status("Reading file contents...");
                await Task.Delay(1500);
                
                ctx.Status("Processing data...");
                await Task.Delay(1000);
            });

        AnsiConsole.MarkupLine("[green]✓ File operation completed successfully![/]");
    }

    private static async Task SimulateNetworkRequestAsync()
    {
        AnsiConsole.MarkupLine("[yellow]Simulating network request...[/]");

        await AnsiConsole.Status()
            .StartAsync("Connecting to server...", async ctx =>
            {
                ctx.Status("Establishing connection...");
                await Task.Delay(800);
                
                ctx.Status("Sending request...");
                await Task.Delay(1200);
                
                ctx.Status("Waiting for response...");
                await Task.Delay(1500);
                
                ctx.Status("Processing response...");
                await Task.Delay(600);
            });

        var table = new Table();
        table.AddColumn("Property");
        table.AddColumn("Value");
        table.AddRow("Status", "[green]200 OK[/]");
        table.AddRow("Response Time", "[blue]3.1s[/]");
        table.AddRow("Data Size", "[cyan]1.2 KB[/]");

        AnsiConsole.Write(table);
        AnsiConsole.MarkupLine("[green]✓ Network request completed![/]");
    }

    private static async Task ShowProgressDemoAsync()
    {
        AnsiConsole.MarkupLine("[yellow]Running progress demonstration...[/]");

        await AnsiConsole.Progress()
            .StartAsync(async ctx =>
            {
                var task1 = ctx.AddTask("[green]Downloading files[/]");
                var task2 = ctx.AddTask("[blue]Processing data[/]");
                var task3 = ctx.AddTask("[red]Uploading results[/]");

                while (!ctx.IsFinished)
                {
                    await Task.Delay(100);
                    
                    task1.Increment(2.5);
                    task2.Increment(1.8);
                    task3.Increment(1.2);
                }
            });

        AnsiConsole.MarkupLine("[green]✓ All tasks completed![/]");
    }

    private static async Task DisplaySystemInfoAsync()
    {
        AnsiConsole.MarkupLine("[yellow]Gathering system information...[/]");
        
        await Task.Delay(500); // Simulate async operation

        var panel = new Panel(
            new Markup($"""
                [bold]System Information[/]
                
                [blue]OS:[/] {Environment.OSVersion}
                [blue]Machine:[/] {Environment.MachineName}
                [blue]User:[/] {Environment.UserName}
                [blue]Runtime:[/] {Environment.Version}
                [blue]Working Directory:[/] {Environment.CurrentDirectory}
                [blue]Processor Count:[/] {Environment.ProcessorCount}
                [blue]Current Time:[/] {DateTime.Now:yyyy-MM-dd HH:mm:ss}
                """))
            .Header("System Info")
            .BorderColor(Color.Green);

        AnsiConsole.Write(panel);
    }
}
